2025-05-27 10:09:21,380  INFO: 测试开始!
2025-05-27 10:09:21,382  INFO: 测试进度：0/1
2025-05-27 10:09:21,382  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 10:09:21,383  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 10:09:21,480  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON><PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-05-27 10:09:21,499  INFO: 开始测试
2025-05-27 10:09:24,630  DEBUG: Generator State: OFF
2025-05-27 10:09:24,835  INFO: 测试结束
2025-05-27 10:09:24,842  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 10:09:24,843  INFO: 测试进度：1/1
2025-05-27 10:09:29,847  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 10:09:29,851  INFO: 测试完成！总共测试耗时：00:00:09
2025-05-27 10:12:40,487  INFO: 测试开始!
2025-05-27 10:12:40,488  INFO: 测试进度：0/1
2025-05-27 10:12:40,489  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 10:12:40,489  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 10:12:40,579  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 10:12:40,594  INFO: 开始测试
2025-05-27 10:12:40,802  INFO: Generator State: OFF
2025-05-27 10:12:41,001  INFO: 测试结束
2025-05-27 10:12:41,008  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 10:12:41,009  INFO: 测试进度：1/1
2025-05-27 10:12:46,009  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 10:12:46,012  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-27 10:13:53,809  INFO: 测试开始!
2025-05-27 10:13:53,811  INFO: 测试进度：0/1
2025-05-27 10:13:53,812  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 10:13:53,812  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 10:13:53,890  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 10:13:53,905  INFO: 开始测试
2025-05-27 10:13:54,109  INFO: Generator State: OFF
2025-05-27 10:13:54,309  INFO: 测试结束
2025-05-27 10:13:54,316  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 10:13:54,319  INFO: 测试进度：1/1
2025-05-27 10:13:59,318  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 10:13:59,322  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-27 10:17:09,689  INFO: 测试开始!
2025-05-27 10:17:09,693  INFO: 测试进度：0/1
2025-05-27 10:17:09,696  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 10:17:09,696  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 10:17:09,781  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 10:17:09,795  INFO: 开始测试
2025-05-27 10:17:10,013  INFO: Generator State: OFF
2025-05-27 10:17:10,212  INFO: 测试结束
2025-05-27 10:17:10,219  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 10:17:10,221  INFO: 测试进度：1/1
2025-05-27 10:17:15,223  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 10:17:15,230  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-27 10:58:41,787  INFO: 测试开始!
2025-05-27 10:58:41,788  INFO: 测试进度：0/1
2025-05-27 10:58:41,789  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 10:58:41,790  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 10:58:41,887  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 10:58:41,901  INFO: 开始测试
2025-05-27 10:58:41,914  INFO: Generator State: OFF
2025-05-27 10:58:41,918  ERROR: 测试终止！测试错误：测试用例执行异常( 'CmwFunc' object has no attribute 'cmw_device_set_gprf_generator_signal_path' )
2025-05-27 10:59:33,347  INFO: 测试开始!
2025-05-27 10:59:33,348  INFO: 测试进度：0/1
2025-05-27 10:59:33,350  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 10:59:33,350  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 10:59:33,433  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 10:59:33,447  INFO: 开始测试
2025-05-27 10:59:33,458  INFO: Generator State: OFF
2025-05-27 10:59:33,460  ERROR: 测试终止！测试错误：测试用例执行异常( 'CmwFunc' object has no attribute 'cmw_device_set_gprf_generator_signal_path' )
2025-05-27 10:59:50,242  INFO: 测试开始!
2025-05-27 10:59:50,244  INFO: 测试进度：0/1
2025-05-27 10:59:50,245  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 10:59:50,246  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 10:59:50,331  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 10:59:50,346  INFO: 开始测试
2025-05-27 10:59:50,561  INFO: Generator State: OFF
2025-05-27 10:59:50,565  ERROR: 测试终止！测试错误：测试用例执行异常( 'CmwFunc' object has no attribute 'cmw_device_set_gprf_generator_signal_path' )
2025-05-27 11:01:27,721  INFO: 测试开始!
2025-05-27 11:01:27,724  INFO: 测试进度：0/1
2025-05-27 11:01:27,724  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 11:01:27,725  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 11:01:27,784  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 11:01:27,800  INFO: 开始测试
2025-05-27 11:01:28,010  INFO: Generator State: OFF
2025-05-27 11:01:28,017  ERROR: 测试终止！测试错误：测试用例执行异常( 'CmwFunc' object has no attribute 'cmw_device_set_gprf_generator_signal_path' )
2025-05-27 11:03:29,015  INFO: 测试开始!
2025-05-27 11:03:29,017  INFO: 测试进度：0/1
2025-05-27 11:03:29,017  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 11:03:29,018  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 11:03:29,115  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 11:03:29,131  INFO: 开始测试
2025-05-27 11:03:29,144  INFO: Generator State: OFF
2025-05-27 11:03:29,743  INFO: 测试结束
2025-05-27 11:03:29,749  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 11:03:29,750  INFO: 测试进度：1/1
2025-05-27 11:03:34,754  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 11:03:34,757  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-27 11:04:44,349  INFO: 测试开始!
2025-05-27 11:04:44,350  INFO: 测试进度：0/1
2025-05-27 11:04:44,352  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 11:04:44,352  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 11:04:44,438  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 11:04:44,454  INFO: 开始测试
2025-05-27 11:04:44,464  INFO: Generator State: OFF
2025-05-27 11:04:45,091  INFO: 测试结束
2025-05-27 11:04:45,116  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 11:04:45,117  INFO: 测试进度：1/1
2025-05-27 11:04:50,121  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 11:04:50,128  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-27 13:49:07,289  INFO: 测试开始!
2025-05-27 13:49:07,291  INFO: 测试进度：0/1
2025-05-27 13:49:07,293  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 13:49:07,294  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 13:49:07,411  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 13:49:07,427  INFO: 开始测试
2025-05-27 13:49:07,440  INFO: Generator State: OFF
2025-05-27 13:49:08,244  INFO: 测试结束
2025-05-27 13:49:08,250  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 13:49:08,251  INFO: 测试进度：1/1
2025-05-27 13:49:13,252  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 13:49:13,255  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-27 13:58:29,041  INFO: 测试开始!
2025-05-27 13:58:29,042  INFO: 测试进度：0/1
2025-05-27 13:58:29,043  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 13:58:29,043  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 13:58:29,134  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 13:58:29,147  INFO: 开始测试
2025-05-27 13:58:29,159  INFO: Generator State: OFF
2025-05-27 13:58:29,967  INFO: level: INV
2025-05-27 13:58:29,968  INFO: 测试结束
2025-05-27 13:58:29,974  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 13:58:29,975  INFO: 测试进度：1/1
2025-05-27 13:58:34,976  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 13:58:34,980  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-27 14:14:11,153  INFO: 测试开始!
2025-05-27 14:14:11,156  INFO: 测试进度：0/1
2025-05-27 14:14:11,156  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 14:14:11,157  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 14:14:11,267  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 14:14:11,283  INFO: 开始测试
2025-05-27 14:14:11,294  INFO: Generator State: OFF
2025-05-27 14:14:12,128  ERROR: 测试终止！测试错误：测试用例执行异常( 'CmwFunc' object has no attribute 'cmw_device_gprf_generator_query_rms_level' )
2025-05-27 14:14:34,857  INFO: 测试开始!
2025-05-27 14:14:34,858  INFO: 测试进度：0/1
2025-05-27 14:14:34,860  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-27 14:14:34,860  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-27 14:14:34,944  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-27 14:14:34,963  INFO: 开始测试
2025-05-27 14:14:34,975  INFO: Generator State: OFF
2025-05-27 14:14:35,827  INFO: level: INV
2025-05-27 14:14:36,034  INFO: 测试结束
2025-05-27 14:14:36,042  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-27 14:14:36,043  INFO: 测试进度：1/1
2025-05-27 14:14:41,043  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-27 14:14:41,045  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-28 09:36:04,413  INFO: 测试开始!
2025-05-28 09:36:04,417  INFO: 测试进度：0/1
2025-05-28 09:36:04,418  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-28 09:36:04,418  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-28 09:36:04,450  ERROR: 测试终止！测试错误：测试用例执行异常( invalid syntax (serial_base_class.py, line 606) )
