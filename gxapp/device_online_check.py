import time
import re
import socket
import logging
import concurrent.futures

from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from gxapp.public_data import device_model, color_print


class DeviceHandShakeCheck:

    def __init__(self, ip_addr: dict, event_engine: EventEngine):
        self.ip_addr = ip_addr
        self.event_engine = event_engine

    def write_error_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.ERROR)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_info_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.INFO)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_debug_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.DEBUG)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def send_data(self, data: str, client: socket.socket):
        data = data.encode("utf-8")
        print("send data: {}".format(data))
        try:
            client.send(data)
        except Exception as e:
            return False
        return True

    def recv_data(self, client: socket.socket):
        data = client.recv(4096)
        print("recv data: {}".format(data))
        try:
            data = data.decode("utf-8").strip()
        except Exception as e:
            return False, None
        return True, data

    def device_identification_query(self, client: socket.socket):
        """
        The response is organized into four fields separated by commas. The field definitions are as follows:
            1. Manufacturer
            2. Model
            3. Serial number
            4. Firmware version
        Returns instrument identification information, such as:
            * Keysight Technologies,N9010B,MY63440212,A.33.03
        """
        cmd = "*IDN?\r\n"
        self.send_data(data=cmd, client=client)
        time.sleep(0.01)
        ret, data = self.recv_data(client=client)
        print(data)
        return data

    def init_device_connect(self, device_ip: str, device_name: str, port: int = 5025):
        try:
            client = socket.socket(family=socket.AF_INET, type=socket.SOCK_STREAM)
            client.settimeout(1)
            client.connect((device_ip, port))
            host_id = self.device_identification_query(client=client)
            temp = re.split(r",", host_id)
            cur_device_name = temp[1].strip()
            client.close()
            if device_model[device_name] != cur_device_name:
                error = "{}设备型号不匹配：预期设备型号：{}--实际设备型号：{}".format(
                    device_name, device_model[device_name], cur_device_name)
                self.write_error_log(msg=error)
                return False, "connection successful, but device model error！"
            else:
                return True, "connection successful"
        except socket.timeout:
            return False, "connect timeout！"
        except socket.error as e:
            return False, "connect error！"

    def handshake_device_test(self, device_info: str):
        device_name = device_info[0]
        device_ip = device_info[1]
        result = dict()
        ret, info = self.init_device_connect(device_ip=device_ip, device_name=device_name)
        if ret is False:
            # print("\033[91m{}: {} \033[0m".format(device_name, info))
            color_print("{}: {}".format(device_name, info))
        else:
            # print("\033[92m{}: {} \033[0m".format(device_name, info))
            color_print("{}: {}".format(device_name, info), 'green')
        result[device_name] = ret
        return result

    def run(self):
        devices = self.ip_addr
        with concurrent.futures.ThreadPoolExecutor() as executor:
            results = executor.map(self.handshake_device_test, devices.items())

        test_result = dict()
        for result in results:
            for k, v in result.items():
                test_result[k] = v
        print(test_result)
        return test_result
