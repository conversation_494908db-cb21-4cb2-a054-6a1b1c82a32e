import time

from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import DeviceRun


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.select_cmw = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")

        # ret = self.cmw_api.cmw_device_query_gprf_generator_state()
        # if ret:
        #     self.write_info_log(msg="Generator State: {}".format(ret))
        #     if ret == "ON":
        #         self.cmw_api.cmw_device_set_gprf_generator_state("OFF")
        #
        # self.cmw_api.cmw_device_system_reset_all()
        #
        # time.sleep(1)
        #
        # self.cmw_api.cmw_device_gprf_generator_set_signal_path("RF1O", "TX1")
        #
        # self.cmw_api.cmw_device_gprf_generator_set_signal_baseband_mode("ARB")
        #
        # self.cmw_api.cmw_device_gprf_generator_set_arb_file_repetition_mode("CONTinuous")
        #
        # self.cmw_api.cmw_device_gprf_generator_set_rms_level(-60)
        #
        # ret = self.cmw_api.cmw_device_gprf_generator_query_peak_envelope_power()
        # if ret:
        #     self.write_info_log("level: {}".format(ret))
        #
        # self.cmw_api.cmw_device_gprf_generator_set_unmod_rf_carrier_frequency(2402, "MHz")
        #
        # arb_file = r"D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar4_8\qpsk_1Rs_24Fs_1000fps_PType2_seed0_4bk_sym2db_grdc.wv"
        # self.cmw_api.cmw_device_gprf_generator_set_arb_file(arb_file)
        #
        # ret = self.cmw_api.cmw_device_gprf_generator_query_arb_file()
        # if ret:
        #     self.write_info_log("file: {}".format(ret))
        #
        # self.cmw_api.cmw_device_set_gprf_generator_state("ON")
        #
        # self.cmw_api.cmw_func_gprf_generator_turn_on_status_wait_load_arb_file()

        # self.uart_handle.interact_with_sagitta_dut_init_device()

        # self.uart_handle.interact_with_sagitta_dut_configure_test_params("bb test_cmw 0 0 1 4 6")

        ret, result = self.uart_handle.sagitta_dut_get_gauss_result("bb test_cmw 0 0 1 4 6")
        print(result)

        self.write_info_log(msg="测试结束")

